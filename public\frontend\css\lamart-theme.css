/* =====================================
LAMART - MODERN ECOMMERCE THEME
Primary Color: #4D734E (Forest Green)
Secondary Color: #5A5863 (Charcoal Grey)
Accent Color: #000000 (Pure Black)
Light Grey: #F8F9FA (Background)
Success: #28A745 (Success Green)
Warning: #FFC107 (Warning Amber)
========================================*/

/* CSS Variables for consistent theming */
:root {
    --primary-color: #4D734E;
    --primary-dark: #3d5a3e;
    --primary-light: #6b9a6d;
    --secondary-color: #5A5863;
    --secondary-light: #8a8a95;
    --accent-color: #000000;
    --light-bg: #F8F9FA;
    --white: #FFFFFF;
    --success: #28A745;
    --warning: #FFC107;
    --danger: #DC3545;
    --border-light: #E9ECEF;
    --text-dark: #212529;
    --text-muted: #6C757D;
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    --border-radius: 0.375rem;
    --border-radius-lg: 0.5rem;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* =====================================
MODERN HEADER STYLING
========================================*/

/* Enhanced Header */
.header.shop {
    box-shadow: var(--shadow);
    background: var(--white);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header.shop .topbar {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    padding: 8px 0;
    font-size: 13px;
}

.header.shop .topbar .list-main li {
    color: var(--white);
    margin-right: 20px;
}

.header.shop .topbar .list-main li i {
    color: var(--white);
    margin-right: 8px;
    font-size: 14px;
}

.header.shop .topbar .list-main li a {
    color: var(--white);
    transition: var(--transition);
    text-decoration: none;
}

.header.shop .topbar .list-main li a:hover {
    color: rgba(255, 255, 255, 0.8);
}

/* Logo Enhancement */
.logo img {
    max-height: 60px;
    width: auto;
    transition: var(--transition);
}

.logo:hover img {
    transform: scale(1.05);
}

/* Modern Search Bar */
.search-bar-top .search-bar {
    background: var(--white);
    border: 2px solid var(--border-light);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.search-bar-top .search-bar:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(77, 115, 78, 0.25);
}

.search-bar-top .search-bar select {
    border: none;
    background: var(--light-bg);
    padding: 12px 15px;
    font-weight: 500;
    color: var(--text-dark);
}

.search-bar-top .search-bar input {
    border: none;
    padding: 12px 15px;
    font-size: 14px;
    flex: 1;
}

.search-bar-top .search-bar .btnn {
    background: var(--primary-color);
    border: none;
    padding: 12px 20px;
    color: var(--white);
    transition: var(--transition);
}

.search-bar-top .search-bar .btnn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

/* Shopping Cart Icon */
.right-bar .shopping {
    position: relative;
}

.right-bar .shopping .dropdown-cart-header {
    background: var(--primary-color);
    color: var(--white);
    padding: 10px 15px;
    border-radius: var(--border-radius);
    transition: var(--transition);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.right-bar .shopping .dropdown-cart-header:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.right-bar .shopping .total-count {
    background: var(--white);
    color: var(--primary-color);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    margin-left: 5px;
}

/* =====================================
MODERN PRODUCT CARDS
========================================*/

/* Enhanced Product Cards */
.single-product {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    margin-bottom: 30px;
    border: 1px solid var(--border-light);
}

.single-product:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.single-product .product-img {
    position: relative;
    overflow: hidden;
}

.single-product .product-img img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: var(--transition);
}

.single-product:hover .product-img img {
    transform: scale(1.05);
}

/* Product Action Buttons */
.single-product .product-img .product-action {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    opacity: 0;
    transform: translateX(20px);
    transition: var(--transition);
}

.single-product:hover .product-img .product-action {
    opacity: 1;
    transform: translateX(0);
}

.single-product .product-img .product-action a {
    width: 40px;
    height: 40px;
    background: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-dark);
    box-shadow: var(--shadow);
    transition: var(--transition);
    text-decoration: none;
}

.single-product .product-img .product-action a:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: scale(1.1);
}

/* Product Content */
.single-product .product-content {
    padding: 20px;
}

.single-product .product-content h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    line-height: 1.4;
}

.single-product .product-content h3 a {
    color: var(--text-dark);
    text-decoration: none;
    transition: var(--transition);
}

.single-product .product-content h3 a:hover {
    color: var(--primary-color);
}

/* Product Price */
.single-product .product-content .product-price {
    margin-bottom: 15px;
}

.single-product .product-content .product-price span {
    font-size: 18px;
    font-weight: 700;
    color: var(--primary-color);
}

.single-product .product-content .product-price .old-price {
    font-size: 14px;
    color: var(--text-muted);
    text-decoration: line-through;
    margin-left: 8px;
    font-weight: 400;
}

/* Product Rating */
.single-product .product-content .rating {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 15px;
}

.single-product .product-content .rating i {
    color: #FFD700;
    font-size: 14px;
}

.single-product .product-content .rating .rating-count {
    font-size: 12px;
    color: var(--text-muted);
    margin-left: 5px;
}

/* Add to Cart Button */
.single-product .product-content .add-to-cart {
    width: 100%;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 12px;
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: var(--transition);
    cursor: pointer;
}

.single-product .product-content .add-to-cart:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

/* =====================================
MODERN BUTTONS & FORMS
========================================*/

/* Primary Buttons */
.btn {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: var(--transition);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    cursor: pointer;
    font-size: 14px;
}

.btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
    color: var(--white);
}

.btn.btn-secondary {
    background: var(--secondary-color);
}

.btn.btn-secondary:hover {
    background: var(--secondary-light);
}

.btn.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn.btn-outline:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* Form Elements */
.form-control,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="search"],
textarea,
select {
    border: 2px solid var(--border-light);
    border-radius: var(--border-radius);
    padding: 12px 15px;
    font-size: 14px;
    transition: var(--transition);
    background: var(--white);
}

.form-control:focus,
input:focus,
textarea:focus,
select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(77, 115, 78, 0.25);
}

/* Modern Pagination */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 40px;
}

.pagination .pagination-list {
    display: flex;
    gap: 5px;
    list-style: none;
    padding: 0;
    margin: 0;
}

.pagination .pagination-list li a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 2px solid var(--border-light);
    border-radius: var(--border-radius);
    color: var(--text-dark);
    text-decoration: none;
    transition: var(--transition);
    font-weight: 500;
}

.pagination .pagination-list li.active a,
.pagination .pagination-list li:hover a {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

/* =====================================
MODERN NAVIGATION & MENU
========================================*/

/* Main Navigation */
.header.shop .header-inner {
    background: var(--white);
    padding: 15px 0;
    border-bottom: 1px solid var(--border-light);
}

.header.shop .nav li a {
    color: var(--text-dark);
    font-weight: 500;
    padding: 10px 15px;
    border-radius: var(--border-radius);
    transition: var(--transition);
    text-decoration: none;
}

.header.shop .nav li:hover a,
.header.shop .nav li.active a {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

/* Dropdown Menus */
.header.shop .nav li .dropdown {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-light);
    padding: 10px 0;
    margin-top: 10px;
}

.header.shop .nav li .dropdown li a {
    padding: 8px 20px;
    color: var(--text-dark);
    border-radius: 0;
}

.header.shop .nav li .dropdown li:hover a {
    background: var(--light-bg);
    color: var(--primary-color);
    transform: none;
}

/* =====================================
MODERN SIDEBAR & FILTERS
========================================*/

/* Sidebar Widgets */
.shop-sidebar .single-widget,
.main-sidebar .single-widget {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
}

.shop-sidebar .single-widget h3,
.main-sidebar .single-widget h3 {
    background: var(--primary-color);
    color: var(--white);
    margin: -25px -25px 20px -25px;
    padding: 15px 25px;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    font-size: 16px;
    font-weight: 600;
}

/* Category List */
.shop-sidebar .categor-list li,
.main-sidebar .categor-list li {
    border-bottom: 1px solid var(--border-light);
    padding: 10px 0;
}

.shop-sidebar .categor-list li:last-child,
.main-sidebar .categor-list li:last-child {
    border-bottom: none;
}

.shop-sidebar .categor-list li a,
.main-sidebar .categor-list li a {
    color: var(--text-dark);
    text-decoration: none;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.shop-sidebar .categor-list li a:hover,
.main-sidebar .categor-list li a:hover {
    color: var(--primary-color);
    padding-left: 10px;
}

/* Price Range Slider */
.shop .range {
    margin: 20px 0;
}

.shop .range #slider-range {
    background: var(--border-light);
    border-radius: var(--border-radius);
    height: 6px;
}

.shop .range .ui-slider-handle {
    background: var(--primary-color);
    border: 3px solid var(--white);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    box-shadow: var(--shadow);
    cursor: pointer;
}

.shop .range .ui-slider-range {
    background: var(--primary-color);
    border-radius: var(--border-radius);
}

/* =====================================
MODERN SHOPPING CART & CHECKOUT
========================================*/

/* Shopping Cart Table */
.shopping-summery {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
}

.shopping-summery thead {
    background: var(--primary-color);
    color: var(--white);
}

.shopping-summery thead th {
    padding: 15px;
    font-weight: 600;
    border: none;
}

.shopping-summery tbody td {
    padding: 20px 15px;
    border-bottom: 1px solid var(--border-light);
    vertical-align: middle;
}

.shopping-summery tbody tr:last-child td {
    border-bottom: none;
}

.shopping-summery .product img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.shopping-summery .product-name a {
    color: var(--text-dark);
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition);
}

.shopping-summery .product-name a:hover {
    color: var(--primary-color);
}

/* Quantity Controls */
.shopping-cart .qty {
    display: flex;
    align-items: center;
    gap: 10px;
}

.shopping-cart .qty .button {
    display: flex;
    border: 2px solid var(--border-light);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.shopping-cart .qty .button input {
    width: 60px;
    text-align: center;
    border: none;
    padding: 8px;
    font-weight: 500;
}

.shopping-cart .qty .button .btn {
    width: 35px;
    height: 35px;
    background: var(--light-bg);
    color: var(--text-dark);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    padding: 0;
}

.shopping-cart .qty .button .btn:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* Cart Total */
.shopping-cart .total-amount {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: 30px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
}

.shopping-cart .total-amount h3 {
    color: var(--text-dark);
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--primary-color);
}

.shopping-cart .total-amount .total-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.shopping-cart .total-amount .total-list li {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid var(--border-light);
}

.shopping-cart .total-amount .total-list li:last-child {
    border-bottom: none;
    font-weight: 600;
    font-size: 18px;
    color: var(--primary-color);
}

/* Coupon Form */
.shopping-cart .total-amount .coupon form {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.shopping-cart .total-amount .coupon input {
    flex: 1;
    border: 2px solid var(--border-light);
    border-radius: var(--border-radius);
    padding: 12px 15px;
}

.shopping-cart .total-amount .coupon .btn {
    padding: 12px 20px;
    white-space: nowrap;
}

/* =====================================
MODERN FOOTER
========================================*/

.footer {
    background: linear-gradient(135deg, var(--text-dark), #1a1a1a);
    color: var(--white);
    padding: 60px 0 20px;
    margin-top: 80px;
}

.footer .single-footer {
    margin-bottom: 40px;
}

.footer .single-footer h4 {
    color: var(--white);
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 25px;
    position: relative;
    padding-bottom: 10px;
}

.footer .single-footer h4::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: var(--primary-color);
    border-radius: var(--border-radius);
}

.footer .single-footer p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 20px;
}

.footer .single-footer .links ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer .single-footer .links ul li {
    margin-bottom: 10px;
}

.footer .single-footer .links ul li a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.footer .single-footer .links ul li a:hover {
    color: var(--primary-color);
    padding-left: 10px;
}

.footer .single-footer .links ul li a i {
    font-size: 14px;
}

/* Social Links */
.footer .social ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    gap: 15px;
}

.footer .social ul li a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    text-decoration: none;
}

.footer .social ul li a:hover {
    background: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: var(--shadow);
}

/* Footer Bottom */
.footer .copyright {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 30px;
    margin-top: 40px;
    text-align: center;
}

.footer .copyright p {
    color: rgba(255, 255, 255, 0.6);
    margin: 0;
}

/* =====================================
MODERN ALERTS & NOTIFICATIONS
========================================*/

.alert {
    border-radius: var(--border-radius-lg);
    padding: 15px 20px;
    margin-bottom: 20px;
    border: none;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: var(--shadow-sm);
}

.alert-success {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success);
    border-left: 4px solid var(--success);
}

.alert-danger {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger);
    border-left: 4px solid var(--danger);
}

.alert-warning {
    background: rgba(255, 193, 7, 0.1);
    color: var(--warning);
    border-left: 4px solid var(--warning);
}

.alert-info {
    background: rgba(77, 115, 78, 0.1);
    color: var(--primary-color);
    border-left: 4px solid var(--primary-color);
}

/* =====================================
RESPONSIVE DESIGN ENHANCEMENTS
========================================*/

@media (max-width: 768px) {
    .header.shop .topbar {
        padding: 5px 0;
        font-size: 12px;
    }

    .header.shop .topbar .list-main li {
        margin-right: 10px;
    }

    .search-bar-top .search-bar {
        flex-direction: column;
    }

    .search-bar-top .search-bar select,
    .search-bar-top .search-bar input {
        width: 100%;
    }

    .single-product .product-img img {
        height: 200px;
    }

    .shopping-summery {
        font-size: 14px;
    }

    .shopping-summery .product img {
        width: 60px;
        height: 60px;
    }

    .footer {
        padding: 40px 0 20px;
        text-align: center;
    }

    .footer .social ul {
        justify-content: center;
    }
}

/* =====================================
UTILITY CLASSES
========================================*/

.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-light { background-color: var(--light-bg) !important; }

.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }

.rounded { border-radius: var(--border-radius) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }

.transition { transition: var(--transition) !important; }
