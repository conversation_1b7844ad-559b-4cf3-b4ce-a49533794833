<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App;
use Mail;
use App\Mail\MailQueue;
use App\Models\User;

class EmailController extends Controller
{
    public function sendNewRegistrationEmail($data)
    {
        $data['name'] = $data['first_name'] . ' ' . $data['last_name'];

        $data['subject'] = 'Registration Successfull';
        $data['view_file'] = 'emails.registered';

        Mail::to($data['email'], $data['name'])->queue(new MailQueue($data));

        return response()->json(['message' => 'Email sent successfully.'], 200);
    }

    public function sendNewRegistrationEmailAdmin($data){
        $data['subject'] = 'New User Registered';
        $data['view_file'] = 'emails.admin.registered';
        $data['name'] = $data['first_name'] . ' ' . $data['last_name'];
        Mail::to(ADMIN_EMAILS)->queue(new MailQueue($data));

    }

    public function sendAdminNewRegistrationEmail($data){
        $data['subject'] = 'Ad<PERSON> has invited you to '.env('APP_NAME');
        $data['view_file'] = 'emails.created';
        $data['name'] = $data['first_name'] . ' ' . $data['last_name'];
        Mail::to($data['email'], $data['name'])->queue(new MailQueue($data));

    }

    public function userStatusChanged($data){
        $data['subject'] = 'Account Status Changed';
        $data['view_file'] = 'emails.user_status';
        $data['name'] = $data['first_name'] . ' ' . $data['last_name'];
        Mail::to($data['email'], $data['name'])->queue(new MailQueue($data));
    }

    public function send_visit_reminders_salesman($reminder)
    {
        $data['locale'] = App::getLocale();

        $data['subject'] = 'Visit Reminder';
        $data['reminder'] = $reminder;
        $data['view_file'] = 'backend.emails.salesman-visit-reminder';

        Mail::to($reminder->salesman->email, $reminder->salesman->first_name . ' ' . $reminder->salesman->last_name)->queue(new MailQueue($data));

        return response()->json(['message' => 'Email sent successfully.'], 200);
    }

    public function send_return($order, $returns, $reason)
    {
        $data = [
            'locale' => App::getLocale(),
            'subject' => 'Order Return Request',
            'order' => $order,
            'returns' => $returns,
            'reason' => $reason,
            'view_file' => 'backend.emails.salesman-return-request'
        ];

        Mail::to($order->user->email, $order->user->first_name . ' ' . $order->user->last_name)->queue(new MailQueue($data));

        $admins = User::where('role', 'admin')->get();
        foreach ($admins as $admin) {
            Mail::to($admin->email, $admin->first_name . ' ' . $admin->last_name)->queue(new MailQueue($data));
        }

        return response()->json(['message' => 'Emails queued successfully.'], 200);
    }

    public function sendOrderDetails($order)
    {
        $data = [
            'locale' => App::getLocale(),
            'subject' => 'New Order Details',
            'order' => $order,
            'view_file' => 'backend.emails.order_details_customer'
        ];

        Mail::to($order->email, $order->first_name . ' ' . $order->last_name)->queue(new MailQueue($data));

        return response()->json(['message' => 'Email sent successfully.'], 200);
    }
}
