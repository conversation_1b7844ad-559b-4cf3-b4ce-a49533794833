@extends('backend.layouts.master')

@section('title', 'Order Detail')

@section('main-content')
<div class="container-fluid py-4">
    <div class="card shadow-sm border-0">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Order Details</h5>
            <!-- <a href="{{ route('orders.pdf', $order->id) }}" class="btn btn-sm btn-light shadow-sm">
                <i class="fas fa-download fa-sm"></i> Generate PDF
            </a> -->
        </div>
        <div class="card-body p-4">
            @if($order)
                <div class="row mb-4">
                    <div class="col-12">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>S.N.</th>
                                    <th>Order No.</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Quantity</th>
                                    <th>Shipping Charge</th>
                                    <th>Total Amount</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>{{ $order->id }}</td>
                                    <td>{{ $order->order_number }}</td>
                                    <td>{{ $order->first_name }} {{ $order->last_name }}</td>
                                    <td>{{ $order->email }}</td>
                                    <td>{{ $order->quantity }}</td>
                                    <td>${{ number_format(@$order->shipping_price ?? 0, 2) }}</td>
                                    <td>${{ number_format($order->total_amount, 2) }}</td>
                                    <td>
                                        @if($order->status == 'new')
                                            <span class="badge badge-primary">{{ $order->status }}</span>
                                        @elseif($order->status == 'process')
                                            <span class="badge badge-warning">{{ $order->status }}</span>
                                        @elseif($order->status == 'delivered')
                                            <span class="badge badge-success">{{ $order->status }}</span>
                                        @else
                                            <span class="badge badge-danger">{{ $order->status }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        <a href="{{ route('salesman.orders.edit', $order->id) }}" class="btn btn-primary btn-sm mr-1" style="height:30px; width:30px; border-radius:50%" data-toggle="tooltip" title="Edit" data-placement="bottom">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-lg-6 mb-4">
                        <div class="order-info p-4 rounded">
                            <h4 class="text-center pb-3">Order Information</h4>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Order Number</strong></td>
                                    <td>: {{ $order->order_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Order Date</strong></td>
                                    <td>: {{ $order->created_at->format('D d M, Y') }} at {{ $order->created_at->format('g:i a') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Quantity</strong></td>
                                    <td>: {{ $order->quantity }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Order Status</strong></td>
                                    <td>: {{ $order->status }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Shipping Charge</strong></td>
                                    <td>: ${{ number_format(@$order->shipping_price ?? 0, 2) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Coupon</strong></td>
                                    <td>: ${{ number_format($order->coupon ?? 0, 2) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Total Amount</strong></td>
                                    <td>: ${{ number_format($order->total_amount, 2) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Payment Method</strong></td>
                                    <td>: {{ ucwords(str_replace('_', ' ', $order->payment_method)) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Payment Status</strong></td>
                                    <td>: {{ $order->payment_status }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="col-lg-6 mb-4">
                        <div class="shipping-info p-4 rounded">
                            <h4 class="text-center pb-3">Shipping Information</h4>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Full Name</strong></td>
                                    <td>: {{ $order->first_name }} {{ $order->last_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Email</strong></td>
                                    <td>: {{ $order->email }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Phone No.</strong></td>
                                    <td>: {{ $order->phone }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Address</strong></td>
                                    <td>: {{ $order->address1 }}{{ $order->address2 ? ', ' . $order->address2 : '' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Country</strong></td>
                                    <td>: {{ $order->country }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Post Code</strong></td>
                                    <td>: {{ $order->post_code }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card p-4">
                            <h5 class="fw-semibold mb-3">Order Items</h5>
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Image</th>
                                        <th>Product</th>
                                        <th>Color</th>
                                        <th>Price</th>
                                        <th>Quantity</th>
                                        <th>Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($order->items as $item)
                                        <tr>
                                            <td>
                                                <img src="{{ asset('storage/' . $item->item_image) }}" alt="{{ $item->product->title }}" class="img-fluid" style="max-width: 100px; height: auto;">
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    {{ $item->product->title }}
                                                </div>
                                            </td>
                                            <td>{{ $item->color_name->name }}</td>
                                            <td>${{ number_format($item->price, 2) }}</td>
                                            <td>{{ $item->quantity }}</td>
                                            <td>${{ number_format($item->price * $item->quantity, 2) }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                @if($order->signature_data)
                    <div class="row">
                        <div class="col-12">
                            <div class="card p-4">
                                <h5 class="fw-semibold mb-3">Customer Signature</h5>
                                <div class="border rounded bg-light p-2">
                                    <img src="{{ $order->signature_data }}" alt="Customer Signature" class="img-fluid" style="max-width: 400px; height: auto;">
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            @else
                <div class="alert alert-warning">Order not found.</div>
            @endif
        </div>
    </div>
</div>

<div class="border rounded p-3 mb-3">
    <h5 class="fw-semibold mb-3">Order History</h5>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>Date</th>
                <th>Salesman</th>
                <th>Action</th>
                <th>Details</th>
            </tr>
        </thead>
        <tbody>
            @foreach($order->histories as $history)
                <tr>
                    <td>{{ $history->created_at->format('M d, Y H:i') }}</td>
                    <td>{{ $history->user->first_name }} {{ $history->user->last_name }}</td>
                    <td>{{ str_replace('_', ' ', ucfirst($history->action)) }}</td>
                    <td>
                        @if($history->action === 'added_item')
                            Added product ID {{ $history->details['product_id'] }} (Color: {{ $history->details['color'] }}) with quantity {{ $history->details['quantity'] }} at ${{ $history->details['price'] }}
                        @elseif($history->action === 'updated_item')
                            Updated product ID {{ $history->details['product_id'] }} (Color: {{ $history->details['color'] }}): Quantity from {{ $history->details['old_quantity'] }} to {{ $history->details['new_quantity'] }}, Price from ${{ $history->details['old_price'] }} to ${{ $history->details['new_price'] }}
                        @elseif($history->action === 'removed_item')
                            Removed product ID {{ $history->details['product_id'] }} (Color: {{ $history->details['color'] }}) with quantity {{ $history->details['quantity'] }} at ${{ $history->details['price'] }}
                        @elseif($history->action === 'edited_order')
                            Edited order details
                        @endif
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
</div>
@endsection

@push('styles')
<style>
    .order-info, .shipping-info {
        background: #ECECEC;
        padding: 20px;
        border-radius: 8px;
    }
    .order-info h4, .shipping-info h4 {
        text-decoration: underline;
        margin-bottom: 1rem;
    }
    .table th, .table td {
        vertical-align: middle;
    }
    .mr-3 {
        margin-right: 1rem;
    }
</style>
@endpush

@push('scripts')
<script>
    $(document).ready(function() {
        // Initialize tooltips
        $('[data-toggle="tooltip"]').tooltip();

        // SweetAlert2 for delete confirmation
        $('.dltBtn').click(function(e) {
            e.preventDefault();
            var form = $(this).closest('form');
            var id = $(this).data('id');
            Swal.fire({
                title: 'Are you sure?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    form.submit();
                }
            });
        });
    });
</script>
@endpush
