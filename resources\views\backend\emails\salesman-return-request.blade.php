<!DOCTYPE html>
<html lang="{{ $locale }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ __('Order Return Request') }}</title>
</head>
<body>
    <h1>{{ __('Order Return Request') }}</h1>
    <p>{{ __('Dear') }} {{ $order->user->first_name }} {{ $order->user->last_name }},</p>
    <p>{{ __('Your return request for order #') }}{{ $order->id }} {{ __('has been submitted.') }}</p>

    <h2>{{ __('Order Details') }}</h2>
    <p><strong>{{ __('Order ID') }}:</strong> {{ $order->id }}</p>
    <p><strong>{{ __('Return Status') }}:</strong> {{ $order->return_status }}</p>
    <p><strong>{{ __('Total') }}:</strong> {{ number_format($order->total, 2) }}</p>

    <h3>{{ __('Return Items') }}</h3>
    <table border="1" cellpadding="5" cellspacing="0">
        <thead>
            <tr>
                <th>{{ __('Product') }}</th>
                <th>{{ __('Quantity') }}</th>
                <th>{{ __('Reason') }}</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($returns as $return)
                <tr>
                    <td>{{ $return['product_id'] }}</td>
                    <td>{{ $return['return_quantity'] }}</td>
                    <td>{{ $reason }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <p>{{ __('We will process your request and update you soon.') }}</p>
    <p>{{ __('Thank you,') }}<br>{{ config('app.name') }}</p>
</body>
</html>
